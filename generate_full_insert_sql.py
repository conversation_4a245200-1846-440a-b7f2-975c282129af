#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để tạo file SQL đầy đủ với tất cả dữ liệu từ CSV
Không cần phụ thuộc vào file CSV khi chạy SQL
"""

import csv
import unicodedata
import re
import time

def remove_vietnamese_accents(text):
    """Chuyển đổi tiếng Việt có dấu thành không dấu"""
    vietnamese_map = {
        'à': 'a', 'á': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
        'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
        'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
        'đ': 'd',
        'è': 'e', 'é': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
        'ê': 'e', 'ề': 'e', 'ế': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
        'ì': 'i', 'í': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
        'ò': 'o', 'ó': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
        'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
        'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
        'ù': 'u', 'ú': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
        'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
        'ỳ': 'y', 'ý': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
    }

    # Chuyển thành chữ thường
    text = text.lower()

    # Thay thế từng ký tự có dấu
    result = ''
    for char in text:
        result += vietnamese_map.get(char, char)

    return result

def create_safe_title(title):
    """Tạo safe_title (slug) từ title"""
    if title.startswith('Thành phố '):
        clean_title = title[10:]
    elif title.startswith('Tỉnh '):
        clean_title = title[5:]
    else:
        clean_title = title
    
    clean_title = clean_title.lower()
    clean_title = remove_vietnamese_accents(clean_title)
    clean_title = re.sub(r'[^a-z0-9]+', '-', clean_title)
    clean_title = clean_title.strip('-')
    
    return clean_title

def is_city(title):
    """Kiểm tra xem có phải là thành phố không"""
    return 1 if title.startswith('Thành phố ') else 0

def create_title_full(prefix, title, province_title):
    """Tạo title_full từ prefix, title và province_title"""
    return f"{prefix.strip()} {title.strip()}, {province_title.strip()}"

def escape_sql_string(text):
    """Escape string cho SQL"""
    return text.replace("'", "''")

def generate_full_sql():
    """Tạo file SQL đầy đủ với tất cả dữ liệu"""
    
    print("🚀 Bắt đầu tạo file SQL đầy đủ...")
    
    # Đọc dữ liệu province
    print("📖 Đọc dữ liệu province...")
    provinces = []
    try:
        with open('province.csv', 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                province_data = {
                    'id': int(row['id']),
                    'pti_id': int(row['pti_id']),
                    'title': row['title'].strip(),
                    'is_city': is_city(row['title']),
                    'safe_title': create_safe_title(row['title'])
                }
                provinces.append(province_data)
    except FileNotFoundError:
        print("❌ Không tìm thấy file province.csv")
        return False
    
    # Sắp xếp theo safe_title
    provinces.sort(key=lambda x: x['safe_title'])
    
    # Tạo mapping pti_id -> province_id
    province_mapping = {p['pti_id']: p['id'] for p in provinces}
    
    # Đọc dữ liệu ward
    print("📖 Đọc dữ liệu ward...")
    wards = []
    try:
        with open('ward.csv', 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                province_pti_id = int(row['province_pti_id'])
                
                if province_pti_id not in province_mapping:
                    print(f"⚠️  Không tìm thấy province cho pti_id: {province_pti_id}")
                    continue
                
                ward_data = {
                    'pti_id': int(row['pti_id']),
                    'province_id': province_mapping[province_pti_id],
                    'prefix': row['prefix'].strip(),
                    'title': row['title'].strip(),
                    'title_full': create_title_full(
                        row['prefix'], 
                        row['title'], 
                        row['province_title']
                    )
                }
                wards.append(ward_data)
    except FileNotFoundError:
        print("❌ Không tìm thấy file ward_new.csv")
        return False
    
    # Tạo file SQL
    print("📝 Tạo file SQL...")
    current_time = int(time.time())
    
    with open('complete_insert_script.sql', 'w', encoding='utf-8') as f:
        # Header
        f.write("""-- =====================================================
-- SCRIPT SQL ĐẦY ĐỦ ĐỂ INSERT TẤT CẢ DỮ LIỆU
-- =====================================================
-- Dự án: Import 34 tỉnh thành và 3,321 xã phường
-- Tạo: 2025-07-15
-- Mục đích: Insert đầy đủ dữ liệu không cần file CSV
-- Database: urbox

USE urbox;

-- =====================================================
-- PHẦN 1: INSERT DỮ LIỆU PROVINCE (34 RECORDS)
-- =====================================================

""")
        
        # Insert provinces
        f.write("INSERT INTO ___province (id, pti_id, title, safe_title, is_city, position, status, created_at, updated_at, created_by, is_new) VALUES\n")
        
        for i, province in enumerate(provinces):
            position = i + 1
            comma = "," if i < len(provinces) - 1 else ";"
            
            f.write(f"({province['id']}, {province['pti_id']}, '{escape_sql_string(province['title'])}', '{province['safe_title']}', {province['is_city']}, {position}, 2, {current_time}, {current_time}, 'import_script', 2){comma}\n")
        
        f.write("""
-- =====================================================
-- PHẦN 2: INSERT DỮ LIỆU WARD (3,321 RECORDS)
-- =====================================================

""")
        
        # Insert wards
        f.write("INSERT INTO ward (pti_id, province_id, prefix, title, title_full, status, created_at, updated_at, created_by, is_merge) VALUES\n")
        
        for i, ward in enumerate(wards):
            comma = "," if i < len(wards) - 1 else ";"
            
            f.write(f"({ward['pti_id']}, {ward['province_id']}, '{escape_sql_string(ward['prefix'])}', '{escape_sql_string(ward['title'])}', '{escape_sql_string(ward['title_full'])}', 2, {current_time}, {current_time}, 'import_script', 2){comma}\n")
        
        # Validation queries
        f.write("""
-- =====================================================
-- PHẦN 3: VALIDATION QUERIES
-- =====================================================

-- Kiểm tra số lượng provinces
SELECT 'PROVINCE COUNT' AS info, COUNT(*) AS count FROM ___province WHERE is_new = 2;

-- Kiểm tra số lượng wards  
SELECT 'WARD COUNT' AS info, COUNT(*) AS count FROM ward WHERE is_merge = 2;

-- Kiểm tra is_city distribution
SELECT 
    'CITY DISTRIBUTION' AS info,
    SUM(CASE WHEN is_city = 1 THEN 1 ELSE 0 END) AS cities,
    SUM(CASE WHEN is_city = 0 THEN 1 ELSE 0 END) AS provinces
FROM ___province WHERE is_new = 2;

-- Kiểm tra ward count theo province
SELECT 
    p.id AS province_id,
    p.title AS province_title,
    COUNT(w.id) AS ward_count
FROM ___province p
LEFT JOIN ward w ON w.province_id = p.id AND w.is_merge = 2
WHERE p.is_new = 2
GROUP BY p.id, p.title
ORDER BY p.position;

-- Kiểm tra dữ liệu mẫu
SELECT 'SAMPLE DATA' AS info;
SELECT id, pti_id, title, safe_title, is_city FROM ___province WHERE is_new = 2 ORDER BY position LIMIT 5;
SELECT id, pti_id, province_id, prefix, title, title_full FROM ward WHERE is_merge = 2 ORDER BY province_id, id LIMIT 5;

/*
EXPECTED RESULTS:
- 34 provinces với is_new = 2
- 3,321 wards với is_merge = 2  
- 5 thành phố (is_city = 1), 29 tỉnh (is_city = 0)
- Tất cả records có timestamp và created_by = 'import_script'

CÁCH SỬ DỤNG:
mysql -u username -p urbox < complete_insert_script.sql
*/
""")
    
    print(f"✅ Hoàn thành!")
    print(f"📊 Thống kê:")
    print(f"   - Provinces: {len(provinces)}")
    print(f"   - Wards: {len(wards)}")
    print(f"📄 File đã tạo: complete_insert_script.sql")
    
    return True

if __name__ == "__main__":
    success = generate_full_sql()
    if success:
        print("\n🎉 Script hoàn thành! Có thể chạy SQL file để import dữ liệu.")
    else:
        print("\n❌ Script thất bại! Kiểm tra lại file CSV.")
