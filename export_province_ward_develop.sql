-- =====================================================
-- EXPORT DỮ LIỆU BẢNG ___PROVINCE VÀ WARD CHO DEVELOP
-- =====================================================
-- Dự án: Import 34 tỉnh thành và 3,321 xã phường
-- Tạo: 2025-07-15
-- Mục đích: Export dữ liệu từ localhost và import vào develop
-- Database: urbox

-- =====================================================
-- PHẦN 1: KIỂM TRA DỮ LIỆU HIỆN TẠI
-- =====================================================


-- Kiểm tra số lượng dữ liệu trong bảng ___province
SELECT 'KIỂM TRA BẢNG ___PROVINCE' as info;
SELECT
    COUNT(*) as total_provinces,
    COUNT(CASE WHEN is_new = 2 THEN 1 END) as new_provinces,
    COUNT(CASE WHEN is_new = 1 THEN 1 END) as old_provinces
FROM ___province;

-- Kiểm tra số lượng dữ liệu trong bảng ward
SELECT 'KIỂM TRA BẢNG WARD' as info;
SELECT
    COUNT(*) as total_wards,
    COUNT(CASE WHEN is_new = 2 THEN 1 END) as new_wards,
    COUNT(CASE WHEN is_new = 1 THEN 1 END) as old_wards
FROM ward;

-- Xem dữ liệu mẫu province mới (is_new = 2)
SELECT 'DỮ LIỆU MẪU PROVINCE MỚI' as info;
SELECT id, pti_id, title, is_city, position, safe_title, is_new
FROM ___province
WHERE is_new = 2
ORDER BY position
LIMIT 10;

-- Xem dữ liệu mẫu ward mới (is_new = 2)
SELECT 'DỮ LIỆU MẪU WARD MỚI' as info;
SELECT id, province_id, pti_id, title, prefix, title_full, is_new
FROM ward
WHERE is_new = 2
ORDER BY province_id, id
LIMIT 10;

-- =====================================================
-- PHẦN 2: EXPORT DỮ LIỆU CHO DEVELOP
-- =====================================================

-- Export bảng ___province (chỉ records mới với is_new = 2)
SELECT 'EXPORT PROVINCE DATA' as info;
SELECT 
    'INSERT INTO ___province (id, mb_id, pti_id, parent_id, title, safe_title, position, is_city, area, ship_price, ship_constant, status, created_at, updated_at, created_by, updated_by, deleted_at, deleted_by, is_new) VALUES' as header
UNION ALL
SELECT CONCAT(
    '(', id, ', ',
    IFNULL(mb_id, 'NULL'), ', ',
    IFNULL(pti_id, 'NULL'), ', ',
    IFNULL(parent_id, 'NULL'), ', ',
    '''', REPLACE(title, '''', ''''''), ''', ',
    '''', REPLACE(IFNULL(safe_title, ''), '''', ''''''), ''', ',
    position, ', ',
    is_city, ', ',
    IFNULL(area, 'NULL'), ', ',
    IFNULL(ship_price, 'NULL'), ', ',
    IFNULL(ship_constant, 'NULL'), ', ',
    status, ', ',
    created_at, ', ',
    updated_at, ', ',
    '''', REPLACE(IFNULL(created_by, ''), '''', ''''''), ''', ',
    '''', REPLACE(IFNULL(updated_by, ''), '''', ''''''), ''', ',
    IFNULL(deleted_at, 'NULL'), ', ',
    IFNULL(CONCAT('''', REPLACE(deleted_by, '''', ''''''), ''''), 'NULL'), ', ',
    is_new,
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY position) = COUNT(*) OVER () THEN ');'
        ELSE '),'
    END
) as insert_statement
FROM ___province 
WHERE is_new = 2 
ORDER BY position;

-- Export bảng ward (chỉ records mới với is_merge = 2)
SELECT 'EXPORT WARD DATA' as info;
SELECT 
    'INSERT INTO ward (id, province_id, district_id, pti_id, prefix, title, title_full, status, created_at, updated_at, created_by, updated_by, deleted_at, deleted_by, is_merge) VALUES' as header
UNION ALL
SELECT CONCAT(
    '(', id, ', ',
    IFNULL(province_id, 'NULL'), ', ',
    IFNULL(district_id, 'NULL'), ', ',
    IFNULL(pti_id, 'NULL'), ', ',
    '''', REPLACE(IFNULL(prefix, ''), '''', ''''''), ''', ',
    '''', REPLACE(title, '''', ''''''), ''', ',
    '''', REPLACE(IFNULL(title_full, ''), '''', ''''''), ''', ',
    status, ', ',
    created_at, ', ',
    updated_at, ', ',
    '''', REPLACE(IFNULL(created_by, ''), '''', ''''''), ''', ',
    '''', REPLACE(IFNULL(updated_by, ''), '''', ''''''), ''', ',
    IFNULL(deleted_at, 'NULL'), ', ',
    IFNULL(CONCAT('''', REPLACE(deleted_by, '''', ''''''), ''''), 'NULL'), ', ',
    is_merge,
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY province_id, id) = COUNT(*) OVER () THEN ');'
        ELSE '),'
    END
) as insert_statement
FROM ward 
WHERE is_merge = 2 
ORDER BY province_id, id;

-- =====================================================
-- PHẦN 3: SCRIPT KIỂM TRA SAU KHI IMPORT
-- =====================================================

-- Kiểm tra sau khi import trên develop
SELECT 'KIỂM TRA SAU IMPORT' as info;

-- Kiểm tra số lượng province
SELECT 
    'Province' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_new = 2 THEN 1 END) as new_records,
    '34' as expected_new_records
FROM ___province;

-- Kiểm tra số lượng ward  
SELECT 
    'Ward' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_merge = 2 THEN 1 END) as new_records,
    '3321' as expected_new_records
FROM ward;

-- Kiểm tra mapping province -> ward
SELECT 
    p.id as province_id,
    p.title as province_title,
    COUNT(w.id) as ward_count
FROM ___province p
LEFT JOIN ward w ON w.province_id = p.id AND w.is_merge = 2
WHERE p.is_new = 2
GROUP BY p.id, p.title
ORDER BY p.position;

-- Kiểm tra dữ liệu tiếng Việt
SELECT 'KIỂM TRA TIẾNG VIỆT' as info;
SELECT id, title, safe_title FROM ___province WHERE is_new = 2 AND title LIKE '%ă%' OR title LIKE '%â%' OR title LIKE '%đ%' LIMIT 5;
SELECT id, title, title_full FROM ward WHERE is_merge = 2 AND (title LIKE '%ă%' OR title LIKE '%â%' OR title LIKE '%đ%') LIMIT 5;

-- =====================================================
-- PHẦN 4: HƯỚNG DẪN SỬ DỤNG
-- =====================================================

/*
HƯỚNG DẪN EXPORT VÀ IMPORT:

1. TRÊN LOCALHOST - EXPORT DỮ LIỆU:
   
   a) Export bảng ___province:
   mysql -u root -proot -h localhost urbox -e "
   SELECT CONCAT('INSERT INTO ___province (id, mb_id, pti_id, parent_id, title, safe_title, position, is_city, area, ship_price, ship_constant, status, created_at, updated_at, created_by, updated_by, deleted_at, deleted_by, is_new) VALUES (', id, ', ', IFNULL(mb_id, 'NULL'), ', ', IFNULL(pti_id, 'NULL'), ', ', IFNULL(parent_id, 'NULL'), ', ''', REPLACE(title, '''', ''''''), ''', ''', REPLACE(IFNULL(safe_title, ''), '''', ''''''), ''', ', position, ', ', is_city, ', ', IFNULL(area, 'NULL'), ', ', IFNULL(ship_price, 'NULL'), ', ', IFNULL(ship_constant, 'NULL'), ', ', status, ', ', created_at, ', ', updated_at, ', ''', REPLACE(IFNULL(created_by, ''), '''', ''''''), ''', ''', REPLACE(IFNULL(updated_by, ''), '''', ''''''), ''', ', IFNULL(deleted_at, 'NULL'), ', ', IFNULL(CONCAT('''', REPLACE(deleted_by, '''', ''''''), ''''), 'NULL'), ', ', is_new, ');') FROM ___province WHERE is_new = 2 ORDER BY position;
   " > province_data_export.sql

   b) Export bảng ward:
   mysql -u root -proot -h localhost urbox -e "
   SELECT CONCAT('INSERT INTO ward (id, province_id, district_id, pti_id, prefix, title, title_full, status, created_at, updated_at, created_by, updated_by, deleted_at, deleted_by, is_merge) VALUES (', id, ', ', IFNULL(province_id, 'NULL'), ', ', IFNULL(district_id, 'NULL'), ', ', IFNULL(pti_id, 'NULL'), ', ''', REPLACE(IFNULL(prefix, ''), '''', ''''''), ''', ''', REPLACE(title, '''', ''''''), ''', ''', REPLACE(IFNULL(title_full, ''), '''', ''''''), ''', ', status, ', ', created_at, ', ', updated_at, ', ''', REPLACE(IFNULL(created_by, ''), '''', ''''''), ''', ''', REPLACE(IFNULL(updated_by, ''), '''', ''''''), ''', ', IFNULL(deleted_at, 'NULL'), ', ', IFNULL(CONCAT('''', REPLACE(deleted_by, '''', ''''''), ''''), 'NULL'), ', ', is_merge, ');') FROM ward WHERE is_merge = 2 ORDER BY province_id, id;
   " > ward_data_export.sql

2. TRÊN DEVELOP - IMPORT DỮ LIỆU:
   
   a) Backup dữ liệu cũ:
   mysqldump -u username -p urbox ___province ward > backup_before_import.sql
   
   b) Import dữ liệu mới:
   mysql -u username -p urbox < province_data_export.sql
   mysql -u username -p urbox < ward_data_export.sql
   
   c) Kiểm tra kết quả:
   mysql -u username -p urbox < export_province_ward_develop.sql

3. KẾT QUẢ MONG ĐỢI:
   - Province: 34 records mới với is_new = 2
   - Ward: 3,321 records mới với is_merge = 2
   - Dữ liệu tiếng Việt hiển thị chính xác
   - Mapping province -> ward đúng

4. LƯU Ý:
   - Đảm bảo charset utf8mb4 để hỗ trợ tiếng Việt
   - Backup dữ liệu trước khi import
   - Kiểm tra quyền truy cập database
   - Verify dữ liệu sau khi import
*/
