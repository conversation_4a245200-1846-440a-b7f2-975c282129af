-- =====================================================
-- IMPORT DỮ LIỆU TỪ FILE CSV VÀO BẢNG ___PROVINCE
-- =====================================================
-- Dự án: Import 34 tỉnh thành và 3,321 xã phường
-- Tạo: 2023-07-15
-- Mục đích: Import dữ liệu từ file province.csv vào bảng ___province
-- Database: urbox

-- =====================================================
-- PHẦN 1: CHUẨN BỊ DỮ LIỆU
-- =====================================================

-- Tạo bảng tạm để import dữ liệu từ CSV
DROP TABLE IF EXISTS tmp_province_import;
CREATE TABLE tmp_province_import (
  id INT NOT NULL,
  pti_id INT NOT NULL,
  title VARCHAR(255) NOT NULL,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Import dữ liệu từ CSV vào bảng tạm
-- Lưu ý: Thay đổi đường dẫn file CSV phù hợp với môi trường
LOAD DATA INFILE '/path/to/province.csv'
INTO TABLE tmp_province_import
FIELDS TERMINATED BY ',' 
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS
(id, pti_id, title);

-- Kiểm tra dữ liệu đã import
SELECT * FROM tmp_province_import ORDER BY id;

-- =====================================================
-- PHẦN 2: XỬ LÝ DỮ LIỆU
-- =====================================================

-- Tạo bảng tạm để xử lý dữ liệu
DROP TABLE IF EXISTS tmp_province_processed;
CREATE TABLE tmp_province_processed (
  id INT NOT NULL,
  pti_id INT NOT NULL,
  title VARCHAR(255) NOT NULL,
  is_city TINYINT(1) DEFAULT 0,
  safe_title VARCHAR(255) DEFAULT '',
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Xử lý dữ liệu và insert vào bảng tạm
INSERT INTO tmp_province_processed (id, pti_id, title, is_city, safe_title)
SELECT 
  id,
  pti_id,
  title,
  -- Xử lý is_city: 1 nếu là thành phố, 0 nếu là tỉnh
  CASE WHEN title LIKE 'Thành phố%' THEN 1 ELSE 0 END AS is_city,
  -- Xử lý safe_title: bỏ "Tỉnh"/"Thành phố", chuyển thành slug không dấu
  CASE 
    WHEN title LIKE 'Thành phố%' THEN LOWER(REPLACE(SUBSTRING(title, 12), ' ', '-'))
    WHEN title LIKE 'Tỉnh%' THEN LOWER(REPLACE(SUBSTRING(title, 6), ' ', '-'))
    ELSE LOWER(REPLACE(title, ' ', '-'))
  END AS safe_title
FROM tmp_province_import;

-- Kiểm tra dữ liệu đã xử lý
SELECT * FROM tmp_province_processed ORDER BY id;

-- =====================================================
-- PHẦN 3: INSERT VÀO BẢNG CHÍNH
-- =====================================================

-- Tạo INSERT statements cho bảng ___province
INSERT INTO ___province (
  id, pti_id, title, safe_title, is_city, 
  position, status, created_at, updated_at, 
  created_by, is_new
)
SELECT 
  p.id,
  p.pti_id,
  p.title,
  p.safe_title,
  p.is_city,
  ROW_NUMBER() OVER (ORDER BY p.safe_title) AS position, -- Sắp xếp theo safe_title
  2 AS status, -- Mặc định status = 2 (active)
  UNIX_TIMESTAMP() AS created_at,
  UNIX_TIMESTAMP() AS updated_at,
  'import_script' AS created_by,
  2 AS is_new -- Mặc định is_new = 2 (new record)
FROM tmp_province_processed p;

-- =====================================================
-- PHẦN 4: KIỂM TRA SAU KHI IMPORT
-- =====================================================

-- Kiểm tra số lượng records đã import
SELECT COUNT(*) AS total_provinces FROM ___province WHERE is_new = 2;

-- Kiểm tra dữ liệu đã import
SELECT id, pti_id, title, is_city, safe_title, position 
FROM ___province 
WHERE is_new = 2
ORDER BY position;

-- Kiểm tra is_city
SELECT 
  SUM(CASE WHEN is_city = 1 THEN 1 ELSE 0 END) AS total_cities,
  SUM(CASE WHEN is_city = 0 THEN 1 ELSE 0 END) AS total_provinces
FROM ___province 
WHERE is_new = 2;

-- =====================================================
-- PHẦN 5: CLEANUP
-- =====================================================

-- Xóa bảng tạm sau khi hoàn thành
-- DROP TABLE IF EXISTS tmp_province_import;
-- DROP TABLE IF EXISTS tmp_province_processed;

-- Lưu ý: Uncomment các lệnh DROP TABLE khi đã kiểm tra xong
