#!/bin/bash

# =====================================================
# SCRIPT BACKUP VÀ EXPORT DỮ LIỆU CHO DEVELOP
# =====================================================
# Dự án: Cập nhật Hệ thống Hành chính Việt Nam
# Tạo: 2025-07-15
# Mục đích: Backup và export dữ liệu từ localhost

# Cấu hình database
DB_HOST="localhost"
DB_USER="root"
DB_PASS="root"
DB_NAME="urbox"
EXPORT_DIR="./exports"
DATE=$(date +"%Y%m%d_%H%M%S")

# Tạo thư mục export nếu chưa có
mkdir -p $EXPORT_DIR

echo "🚀 Bắt đầu backup và export dữ liệu..."
echo "📅 Thời gian: $(date)"
echo "📁 Thư mục export: $EXPORT_DIR"

# =====================================================
# PHẦN 1: BACKUP DỮ LIỆU CŨ (NẾU CÓ)
# =====================================================

echo ""
echo "📦 BACKUP DỮ LIỆU CŨ..."

# Backup toàn bộ database urbox
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASS \
    --single-transaction \
    --routines \
    --triggers \
    --complete-insert \
    $DB_NAME > "$EXPORT_DIR/urbox_full_backup_$DATE.sql"

if [ $? -eq 0 ]; then
    echo "✅ Backup toàn bộ database thành công: urbox_full_backup_$DATE.sql"
else
    echo "❌ Lỗi backup toàn bộ database"
    exit 1
fi

# =====================================================
# PHẦN 2: EXPORT DỮ LIỆU HÀNH CHÍNH
# =====================================================

echo ""
echo "📤 EXPORT DỮ LIỆU HÀNH CHÍNH..."

# Export bảng tinhthanh
echo "📋 Export bảng tinhthanh..."
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASS \
    --single-transaction \
    --complete-insert \
    --no-create-info \
    $DB_NAME tinhthanh > "$EXPORT_DIR/tinhthanh_data_$DATE.sql"

if [ $? -eq 0 ]; then
    echo "✅ Export tinhthanh thành công"
else
    echo "❌ Lỗi export tinhthanh"
fi

# Export bảng xaphuong
echo "📋 Export bảng xaphuong..."
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASS \
    --single-transaction \
    --complete-insert \
    --no-create-info \
    $DB_NAME xaphuong > "$EXPORT_DIR/xaphuong_data_$DATE.sql"

if [ $? -eq 0 ]; then
    echo "✅ Export xaphuong thành công"
else
    echo "❌ Lỗi export xaphuong"
fi

# Export bảng ward_geometry
echo "📋 Export bảng ward_geometry..."
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASS \
    --single-transaction \
    --complete-insert \
    --no-create-info \
    $DB_NAME ward_geometry > "$EXPORT_DIR/ward_geometry_data_$DATE.sql"

if [ $? -eq 0 ]; then
    echo "✅ Export ward_geometry thành công"
else
    echo "❌ Lỗi export ward_geometry"
fi

# =====================================================
# PHẦN 3: EXPORT SCHEMA (CẤU TRÚC BẢNG)
# =====================================================

echo ""
echo "🏗️ EXPORT SCHEMA..."

# Export schema cho 3 bảng
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASS \
    --no-data \
    --single-transaction \
    $DB_NAME tinhthanh xaphuong ward_geometry > "$EXPORT_DIR/admin_schema_$DATE.sql"

if [ $? -eq 0 ]; then
    echo "✅ Export schema thành công"
else
    echo "❌ Lỗi export schema"
fi

# =====================================================
# PHẦN 4: TẠO FILE IMPORT TỔNG HỢP
# =====================================================

echo ""
echo "📦 TẠO FILE IMPORT TỔNG HỢP..."

COMBINED_FILE="$EXPORT_DIR/urbox_admin_complete_$DATE.sql"

cat > $COMBINED_FILE << 'EOF'
-- =====================================================
-- IMPORT DỮ LIỆU HÀNH CHÍNH VIỆT NAM - DEVELOP
-- =====================================================
-- Tạo: $(date)
-- Database: urbox
-- Bao gồm: tinhthanh (34), xaphuong (3321), ward_geometry (3312)

USE urbox;

-- Tắt kiểm tra foreign key tạm thời
SET FOREIGN_KEY_CHECKS = 0;
SET AUTOCOMMIT = 0;

START TRANSACTION;

EOF

# Thêm schema
echo "-- SCHEMA" >> $COMBINED_FILE
cat "$EXPORT_DIR/admin_schema_$DATE.sql" >> $COMBINED_FILE

# Thêm dữ liệu
echo "" >> $COMBINED_FILE
echo "-- DỮ LIỆU TINHTHANH" >> $COMBINED_FILE
cat "$EXPORT_DIR/tinhthanh_data_$DATE.sql" >> $COMBINED_FILE

echo "" >> $COMBINED_FILE
echo "-- DỮ LIỆU XAPHUONG" >> $COMBINED_FILE
cat "$EXPORT_DIR/xaphuong_data_$DATE.sql" >> $COMBINED_FILE

echo "" >> $COMBINED_FILE
echo "-- DỮ LIỆU WARD_GEOMETRY" >> $COMBINED_FILE
cat "$EXPORT_DIR/ward_geometry_data_$DATE.sql" >> $COMBINED_FILE

# Thêm phần kết thúc
cat >> $COMBINED_FILE << 'EOF'

COMMIT;

-- Bật lại kiểm tra foreign key
SET FOREIGN_KEY_CHECKS = 1;
SET AUTOCOMMIT = 1;

-- KIỂM TRA KẾT QUẢ
SELECT 'KIỂM TRA SỐ LƯỢNG DỮ LIỆU' as info;
SELECT 
    'tinhthanh' as table_name, 
    COUNT(*) as record_count,
    '34' as expected_count
FROM tinhthanh
UNION ALL
SELECT 
    'xaphuong' as table_name, 
    COUNT(*) as record_count,
    '3321' as expected_count
FROM xaphuong
UNION ALL
SELECT 
    'ward_geometry' as table_name, 
    COUNT(*) as record_count,
    '3312' as expected_count
FROM ward_geometry;

SELECT 'IMPORT HOÀN THÀNH!' as status;
EOF

echo "✅ Tạo file import tổng hợp thành công: $COMBINED_FILE"

# =====================================================
# PHẦN 5: THỐNG KÊ VÀ BÁO CÁO
# =====================================================

echo ""
echo "📊 THỐNG KÊ DỮ LIỆU..."

# Kiểm tra số lượng records
TINH_COUNT=$(mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -se "SELECT COUNT(*) FROM tinhthanh")
XA_COUNT=$(mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -se "SELECT COUNT(*) FROM xaphuong")
GEO_COUNT=$(mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -se "SELECT COUNT(*) FROM ward_geometry")

echo "📈 Số lượng dữ liệu:"
echo "   - Tỉnh thành: $TINH_COUNT records"
echo "   - Xã phường: $XA_COUNT records"
echo "   - Ward geometry: $GEO_COUNT records"

# Tính kích thước file
echo ""
echo "💾 Kích thước file export:"
ls -lh $EXPORT_DIR/*$DATE* | awk '{print "   - " $9 ": " $5}'

echo ""
echo "🎉 HOÀN THÀNH EXPORT!"
echo "📁 Các file đã tạo trong thư mục: $EXPORT_DIR"
echo "🚀 File chính để import: urbox_admin_complete_$DATE.sql"
echo ""
echo "📋 HƯỚNG DẪN IMPORT TRÊN DEVELOP:"
echo "   1. Upload file urbox_admin_complete_$DATE.sql lên server develop"
echo "   2. Chạy lệnh: mysql -u username -p urbox < urbox_admin_complete_$DATE.sql"
echo "   3. Kiểm tra kết quả trong script"
