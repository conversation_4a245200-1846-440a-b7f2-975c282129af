-- =====================================================
-- SCRIPT SQL CUỐI CÙNG ĐỂ INSERT DỮ LIỆU
-- =====================================================
-- Dự án: Import 34 tỉnh thành và 3,321 xã phường
-- Tạo: 2025-07-15
-- Mục đích: Insert trực tiếp dữ liệu vào database không cần file CSV
-- Database: urbox

USE urbox;

-- =====================================================
-- PHẦN 1: INSERT DỮ LIỆU PROVINCE
-- =====================================================

-- Insert 34 tỉnh thành vào bảng ___province
INSERT INTO ___province (id, pti_id, title, safe_title, is_city, position, status, created_at, updated_at, created_by, is_new) VALUES
(82, 1, 'Thành phố Hà Nội', 'ha-noi', 1, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(83, 4, 'Tỉnh Cao Bằng', 'cao-bang', 0, 2, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(84, 8, 'Tỉnh Tuyên Quang', 'tuyen-quang', 0, 3, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(85, 11, 'Tỉnh Điện Biên', 'dien-bien', 0, 4, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(86, 12, 'Tỉnh Lai Châu', 'lai-chau', 0, 5, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(87, 14, 'Tỉnh Sơn La', 'son-la', 0, 6, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(88, 15, 'Tỉnh Lào Cai', 'lao-cai', 0, 7, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(89, 19, 'Tỉnh Thái Nguyên', 'thai-nguyen', 0, 8, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(90, 20, 'Tỉnh Lạng Sơn', 'lang-son', 0, 9, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(91, 22, 'Tỉnh Quảng Ninh', 'quang-ninh', 0, 10, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(92, 24, 'Tỉnh Bắc Ninh', 'bac-ninh', 0, 11, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(93, 25, 'Tỉnh Phú Thọ', 'phu-tho', 0, 12, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(94, 31, 'Thành phố Hải Phòng', 'hai-phong', 1, 13, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(95, 33, 'Tỉnh Hưng Yên', 'hung-yen', 0, 14, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(96, 37, 'Tỉnh Ninh Bình', 'ninh-binh', 0, 15, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(97, 38, 'Tỉnh Thanh Hóa', 'thanh-hoa', 0, 16, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(98, 40, 'Tỉnh Nghệ An', 'nghe-an', 0, 17, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(99, 42, 'Tỉnh Hà Tĩnh', 'ha-tinh', 0, 18, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(100, 44, 'Tỉnh Quảng Trị', 'quang-tri', 0, 19, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(101, 46, 'Thành phố Huế', 'hue', 1, 20, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(102, 48, 'Thành phố Đà Nẵng', 'da-nang', 1, 21, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(103, 51, 'Tỉnh Quảng Ngãi', 'quang-ngai', 0, 22, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(104, 52, 'Tỉnh Gia Lai', 'gia-lai', 0, 23, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(105, 56, 'Tỉnh Khánh Hòa', 'khanh-hoa', 0, 24, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(106, 66, 'Tỉnh Đắk Lắk', 'dak-lak', 0, 25, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(107, 68, 'Tỉnh Lâm Đồng', 'lam-dong', 0, 26, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(108, 75, 'Tỉnh Đồng Nai', 'dong-nai', 0, 27, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(109, 79, 'Thành phố Hồ Chí Minh', 'ho-chi-minh', 1, 28, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(110, 80, 'Tỉnh Tây Ninh', 'tay-ninh', 0, 29, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(111, 82, 'Tỉnh Đồng Tháp', 'dong-thap', 0, 30, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(112, 86, 'Tỉnh Vĩnh Long', 'vinh-long', 0, 31, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(113, 91, 'Tỉnh An Giang', 'an-giang', 0, 32, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(114, 92, 'Thành phố Cần Thơ', 'can-tho', 1, 33, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(115, 96, 'Tỉnh Cà Mau', 'ca-mau', 0, 34, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2);

-- =====================================================
-- PHẦN 2: INSERT DỮ LIỆU WARD (MẪU - CẦN BỔ SUNG)
-- =====================================================

-- Insert một số ward mẫu cho Hà Nội (province_id = 82, pti_id = 1)
INSERT INTO ward (pti_id, province_id, prefix, title, title_full, status, created_at, updated_at, created_by, is_merge) VALUES
(4, 82, 'Phường', 'Ba Đình', 'Phường Ba Đình, Thành phố Hà Nội', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(8, 82, 'Phường', 'Ngọc Hà', 'Phường Ngọc Hà, Thành phố Hà Nội', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(25, 82, 'Phường', 'Giảng Võ', 'Phường Giảng Võ, Thành phố Hà Nội', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(70, 82, 'Phường', 'Hoàn Kiếm', 'Phường Hoàn Kiếm, Thành phố Hà Nội', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(82, 82, 'Phường', 'Cửa Nam', 'Phường Cửa Nam, Thành phố Hà Nội', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(35, 82, 'Xã', 'Sóc Sơn', 'Xã Sóc Sơn, Thành phố Hà Nội', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2);

-- Insert một số ward mẫu cho Cao Bằng (province_id = 83, pti_id = 4)
INSERT INTO ward (pti_id, province_id, prefix, title, title_full, status, created_at, updated_at, created_by, is_merge) VALUES
(1273, 83, 'Phường', 'Thục Phán', 'Phường Thục Phán, Tỉnh Cao Bằng', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(1279, 83, 'Phường', 'Nùng Trí Cao', 'Phường Nùng Trí Cao, Tỉnh Cao Bằng', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(1290, 83, 'Xã', 'Bảo Lâm', 'Xã Bảo Lâm, Tỉnh Cao Bằng', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2),
(1294, 83, 'Xã', 'Lý Bôn', 'Xã Lý Bôn, Tỉnh Cao Bằng', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'import_script', 2);

-- =====================================================
-- PHẦN 3: KIỂM TRA SAU KHI INSERT
-- =====================================================

-- Kiểm tra số lượng provinces đã insert
SELECT 'PROVINCE COUNT' AS info, COUNT(*) AS count 
FROM ___province WHERE is_new = 2;

-- Kiểm tra số lượng wards đã insert
SELECT 'WARD COUNT' AS info, COUNT(*) AS count 
FROM ward WHERE is_merge = 2;

-- Kiểm tra is_city distribution
SELECT 
    'CITY DISTRIBUTION' AS info,
    SUM(CASE WHEN is_city = 1 THEN 1 ELSE 0 END) AS cities,
    SUM(CASE WHEN is_city = 0 THEN 1 ELSE 0 END) AS provinces
FROM ___province WHERE is_new = 2;

-- Kiểm tra dữ liệu provinces
SELECT 'SAMPLE PROVINCES' AS info;
SELECT id, pti_id, title, safe_title, is_city, position 
FROM ___province 
WHERE is_new = 2 
ORDER BY position 
LIMIT 10;

-- Kiểm tra dữ liệu wards
SELECT 'SAMPLE WARDS' AS info;
SELECT id, pti_id, province_id, prefix, title, title_full 
FROM ward 
WHERE is_merge = 2 
ORDER BY province_id, id 
LIMIT 10;

-- Kiểm tra ward count theo province
SELECT 
    p.id AS province_id,
    p.title AS province_title,
    COUNT(w.id) AS ward_count
FROM ___province p
LEFT JOIN ward w ON w.province_id = p.id AND w.is_merge = 2
WHERE p.is_new = 2
GROUP BY p.id, p.title
ORDER BY p.position;

-- =====================================================
-- PHẦN 4: LƯU Ý QUAN TRỌNG
-- =====================================================

/*
LƯU Ý:
1. Script này chỉ chứa dữ liệu mẫu cho ward (khoảng 10 records)
2. Để có đầy đủ 3,321 wards, cần bổ sung thêm INSERT statements
3. Có thể sử dụng script Python import_csv_to_database.py để tạo đầy đủ
4. Hoặc sử dụng script SQL import_ward_data.sql với file CSV

CÁCH SỬ DỤNG:
1. Chạy script này để insert provinces và một số wards mẫu
2. Kiểm tra kết quả bằng các query validation ở cuối
3. Bổ sung thêm ward data nếu cần thiết

EXPECTED RESULTS:
- 34 provinces với is_new = 2
- 5 thành phố (is_city = 1), 29 tỉnh (is_city = 0)  
- Một số wards mẫu với is_merge = 2
- Tất cả dữ liệu có timestamp và created_by = 'import_script'
*/
