#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Demo script để test việc xử lý dữ liệu từ CSV
Hiển thị kết quả xử lý cho một vài record mẫu
"""

import unicodedata
import re

def remove_vietnamese_accents(text):
    """Chuyển đổi tiếng Việt có dấu thành không dấu"""
    text = unicodedata.normalize('NFD', text)
    text = ''.join(char for char in text if unicodedata.category(char) != 'Mn')
    return text

def create_safe_title(title):
    """Tạo safe_title (slug) từ title"""
    # Bỏ "Tỉnh" hoặc "Thành phố"
    if title.startswith('Thành phố '):
        clean_title = title[10:]
    elif title.startswith('Tỉnh '):
        clean_title = title[5:]
    else:
        clean_title = title
    
    # Chuyển thành chữ thường
    clean_title = clean_title.lower()
    
    # Bỏ dấu tiếng Việt
    clean_title = remove_vietnamese_accents(clean_title)
    
    # Thay thế khoảng trắng và ký tự đặc biệt bằng dấu gạch ngang
    clean_title = re.sub(r'[^a-z0-9]+', '-', clean_title)
    
    # Bỏ dấu gạch ngang ở đầu và cuối
    clean_title = clean_title.strip('-')
    
    return clean_title

def is_city(title):
    """Kiểm tra xem có phải là thành phố không"""
    return 1 if title.startswith('Thành phố ') else 0

def create_title_full(prefix, title, province_title):
    """Tạo title_full từ prefix, title và province_title"""
    return f"{prefix.strip()} {title.strip()}, {province_title.strip()}"

def demo_province_processing():
    """Demo xử lý dữ liệu province"""
    print("🏛️  DEMO XỬ LÝ DỮ LIỆU PROVINCE")
    print("=" * 60)
    
    # Dữ liệu mẫu từ province.csv
    sample_provinces = [
        {"id": 82, "pti_id": 1, "title": "Thành phố Hà Nội"},
        {"id": 83, "pti_id": 4, "title": "Tỉnh Cao Bằng"},
        {"id": 94, "pti_id": 31, "title": "Thành phố Hải Phòng"},
        {"id": 109, "pti_id": 79, "title": "Thành phố Hồ Chí Minh"},
        {"id": 97, "pti_id": 38, "title": "Tỉnh Thanh Hóa"}
    ]
    
    print(f"{'ID':<4} {'PTI_ID':<7} {'TITLE':<25} {'IS_CITY':<8} {'SAFE_TITLE':<20}")
    print("-" * 70)
    
    for province in sample_provinces:
        safe_title = create_safe_title(province['title'])
        city_flag = is_city(province['title'])
        
        print(f"{province['id']:<4} {province['pti_id']:<7} {province['title']:<25} {city_flag:<8} {safe_title:<20}")
    
    print("\n📊 Thống kê:")
    cities = sum(1 for p in sample_provinces if is_city(p['title']))
    provinces = len(sample_provinces) - cities
    print(f"   - Thành phố: {cities}")
    print(f"   - Tỉnh: {provinces}")

def demo_ward_processing():
    """Demo xử lý dữ liệu ward"""
    print("\n🏘️  DEMO XỬ LÝ DỮ LIỆU WARD")
    print("=" * 60)
    
    # Dữ liệu mẫu từ ward_new.csv
    sample_wards = [
        {"pti_id": 4, "prefix": "Phường", "province_pti_id": 1, "province_title": "Thành phố Hà Nội", "title": "Ba Đình"},
        {"pti_id": 8, "prefix": "Phường", "province_pti_id": 1, "province_title": "Thành phố Hà Nội", "title": "Ngọc Hà"},
        {"pti_id": 1273, "prefix": "Phường", "province_pti_id": 4, "province_title": "Tỉnh Cao Bằng", "title": "Thục Phán"},
        {"pti_id": 1290, "prefix": "Xã", "province_pti_id": 4, "province_title": "Tỉnh Cao Bằng", "title": "Bảo Lâm"},
        {"pti_id": 35, "prefix": "Xã", "province_pti_id": 1, "province_title": "Thành phố Hà Nội", "title": "Sóc Sơn"}
    ]
    
    # Mapping province_pti_id -> province_id (giả lập)
    province_mapping = {1: 82, 4: 83, 31: 94, 79: 109, 38: 97}
    
    print(f"{'PTI_ID':<7} {'PROV_ID':<8} {'PREFIX':<8} {'TITLE':<15} {'TITLE_FULL':<50}")
    print("-" * 90)
    
    for ward in sample_wards:
        province_id = province_mapping.get(ward['province_pti_id'], 'N/A')
        title_full = create_title_full(ward['prefix'], ward['title'], ward['province_title'])
        
        print(f"{ward['pti_id']:<7} {province_id:<8} {ward['prefix']:<8} {ward['title']:<15} {title_full:<50}")
    
    print("\n📊 Thống kê:")
    phuong_count = sum(1 for w in sample_wards if w['prefix'] == 'Phường')
    xa_count = sum(1 for w in sample_wards if w['prefix'] == 'Xã')
    print(f"   - Phường: {phuong_count}")
    print(f"   - Xã: {xa_count}")

def demo_sql_generation():
    """Demo tạo SQL statements"""
    print("\n💾 DEMO TẠO SQL STATEMENTS")
    print("=" * 60)
    
    # Sample province data
    province = {
        'id': 82,
        'pti_id': 1,
        'title': 'Thành phố Hà Nội',
        'is_city': 1,
        'safe_title': 'ha-noi'
    }
    
    # Sample ward data
    ward = {
        'pti_id': 4,
        'province_id': 82,
        'prefix': 'Phường',
        'title': 'Ba Đình',
        'title_full': 'Phường Ba Đình, Thành phố Hà Nội'
    }
    
    print("🏛️  SQL cho Province:")
    province_sql = f"""INSERT INTO ___province (
    id, pti_id, title, safe_title, is_city, position, 
    status, created_at, updated_at, created_by, is_new
) VALUES (
    {province['id']}, {province['pti_id']}, '{province['title']}', 
    '{province['safe_title']}', {province['is_city']}, 1,
    2, 1642234567, 1642234567, 'import_script', 2
);"""
    print(province_sql)
    
    print("\n🏘️  SQL cho Ward:")
    ward_sql = f"""INSERT INTO ward (
    pti_id, province_id, prefix, title, title_full,
    status, created_at, updated_at, created_by, is_merge
) VALUES (
    {ward['pti_id']}, {ward['province_id']}, '{ward['prefix']}', 
    '{ward['title']}', '{ward['title_full']}',
    2, 1642234567, 1642234567, 'import_script', 2
);"""
    print(ward_sql)

def demo_validation():
    """Demo validation queries"""
    print("\n✅ DEMO VALIDATION QUERIES")
    print("=" * 60)
    
    validation_queries = [
        "-- Kiểm tra số lượng provinces",
        "SELECT COUNT(*) AS total_provinces FROM ___province WHERE is_new = 2;",
        "",
        "-- Kiểm tra số lượng wards", 
        "SELECT COUNT(*) AS total_wards FROM ward WHERE is_merge = 2;",
        "",
        "-- Kiểm tra is_city distribution",
        "SELECT",
        "    SUM(CASE WHEN is_city = 1 THEN 1 ELSE 0 END) AS cities,",
        "    SUM(CASE WHEN is_city = 0 THEN 1 ELSE 0 END) AS provinces",
        "FROM ___province WHERE is_new = 2;",
        "",
        "-- Kiểm tra ward count theo province",
        "SELECT",
        "    p.id AS province_id,",
        "    p.title AS province_title,",
        "    COUNT(w.id) AS ward_count",
        "FROM ___province p",
        "LEFT JOIN ward w ON w.province_id = p.id AND w.is_merge = 2",
        "WHERE p.is_new = 2",
        "GROUP BY p.id, p.title",
        "ORDER BY p.position;"
    ]
    
    for query in validation_queries:
        print(query)

def main():
    """Hàm chính để chạy demo"""
    print("🚀 DEMO XỬ LÝ DỮ LIỆU IMPORT CSV")
    print("Mô phỏng quá trình xử lý dữ liệu từ file CSV vào database")
    print("=" * 80)
    
    # Demo xử lý province
    demo_province_processing()
    
    # Demo xử lý ward
    demo_ward_processing()
    
    # Demo tạo SQL
    demo_sql_generation()
    
    # Demo validation
    demo_validation()
    
    print("\n" + "=" * 80)
    print("✅ Demo hoàn thành!")
    print("📝 Để chạy script thực tế, sử dụng: python import_csv_to_database.py")

if __name__ == "__main__":
    main()
