#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script để kiểm tra việc xử lý tiếng Việt có dấu thành slug không dấu
"""

import re

def remove_vietnamese_accents(text):
    """Chuyển đổi tiếng Việt có dấu thành không dấu"""
    vietnamese_map = {
        'à': 'a', 'á': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
        'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
        'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
        'đ': 'd',
        'è': 'e', 'é': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
        'ê': 'e', 'ề': 'e', 'ế': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
        'ì': 'i', 'í': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
        'ò': 'o', 'ó': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
        'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
        'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
        'ù': 'u', 'ú': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
        'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
        'ỳ': 'y', 'ý': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
    }
    
    # Chuyển thành chữ thường
    text = text.lower()
    
    # Thay thế từng ký tự có dấu
    result = ''
    for char in text:
        result += vietnamese_map.get(char, char)
    
    return result

def create_safe_title(title):
    """Tạo safe_title (slug) từ title"""
    if title.startswith('Thành phố '):
        clean_title = title[10:]
    elif title.startswith('Tỉnh '):
        clean_title = title[5:]
    else:
        clean_title = title
    
    clean_title = clean_title.lower()
    clean_title = remove_vietnamese_accents(clean_title)
    clean_title = re.sub(r'[^a-z0-9]+', '-', clean_title)
    clean_title = clean_title.strip('-')
    
    return clean_title

def test_vietnamese_processing():
    """Test việc xử lý tiếng Việt"""
    print("🧪 TEST XỬ LÝ TIẾNG VIỆT CÓ DẤU THÀNH SLUG")
    print("=" * 60)
    
    test_cases = [
        "Thành phố Hà Nội",
        "Thành phố Đà Nẵng", 
        "Thành phố Hồ Chí Minh",
        "Thành phố Hải Phòng",
        "Thành phố Cần Thơ",
        "Tỉnh Cao Bằng",
        "Tỉnh Đắk Lắk",
        "Tỉnh Điện Biên",
        "Tỉnh Lâm Đồng",
        "Tỉnh Nghệ An",
        "Tỉnh Quảng Ninh",
        "Tỉnh Thái Nguyên",
        "Tỉnh Thanh Hóa",
        "Tỉnh Tuyên Quang",
        "Tỉnh Vĩnh Long"
    ]
    
    print(f"{'TITLE GỐC':<25} {'SAFE_TITLE':<20} {'IS_CITY':<8}")
    print("-" * 55)
    
    for title in test_cases:
        safe_title = create_safe_title(title)
        is_city = 1 if title.startswith('Thành phố ') else 0
        
        print(f"{title:<25} {safe_title:<20} {is_city:<8}")
    
    print("\n✅ Kiểm tra các trường hợp đặc biệt:")
    special_cases = [
        ("Đà Nẵng", "da-nang"),
        ("Đắk Lắk", "dak-lak"), 
        ("Điện Biên", "dien-bien"),
        ("Hồ Chí Minh", "ho-chi-minh"),
        ("Nghệ An", "nghe-an"),
        ("Quảng Ninh", "quang-ninh"),
        ("Thái Nguyên", "thai-nguyen"),
        ("Vĩnh Long", "vinh-long")
    ]
    
    print(f"{'INPUT':<15} {'EXPECTED':<15} {'ACTUAL':<15} {'STATUS':<10}")
    print("-" * 60)
    
    for input_text, expected in special_cases:
        actual = remove_vietnamese_accents(input_text.lower()).replace(' ', '-')
        status = "✅ PASS" if actual == expected else "❌ FAIL"
        print(f"{input_text:<15} {expected:<15} {actual:<15} {status:<10}")

if __name__ == "__main__":
    test_vietnamese_processing()
