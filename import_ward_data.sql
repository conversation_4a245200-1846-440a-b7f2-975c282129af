-- =====================================================
-- IMPORT DỮ LIỆU TỪ FILE CSV VÀO BẢNG WARD
-- =====================================================
-- Dự án: Import 34 tỉnh thành và 3,321 xã phường
-- Tạo: 2023-07-15
-- Mục đích: Import dữ liệu từ file ward_new.csv vào bảng ward
-- Database: urbox

-- =====================================================
-- PHẦN 1: CHUẨN BỊ DỮ LIỆU
-- =====================================================

-- Tạo bảng tạm để import dữ liệu từ CSV
DROP TABLE IF EXISTS tmp_ward_import;
CREATE TABLE tmp_ward_import (
  pti_id INT NOT NULL,
  prefix VARCHAR(50) DEFAULT '',
  province_pti_id INT NOT NULL,
  province_title VARCHAR(255) DEFAULT '',
  title VARCHAR(255) NOT NULL,
  PRIMARY KEY (pti_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Import dữ liệu từ CSV vào bảng tạm
-- Lưu ý: Thay đổi đường dẫn file CSV phù hợp với môi trường
LOAD DATA INFILE '/path/to/ward_new.csv'
INTO TABLE tmp_ward_import
FIELDS TERMINATED BY ',' 
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS
(pti_id, prefix, province_pti_id, province_title, title);

-- Kiểm tra dữ liệu đã import
SELECT COUNT(*) AS total_wards FROM tmp_ward_import;
SELECT * FROM tmp_ward_import ORDER BY province_pti_id, pti_id LIMIT 10;

-- =====================================================
-- PHẦN 2: XỬ LÝ DỮ LIỆU
-- =====================================================

-- Tạo bảng tạm để xử lý dữ liệu
DROP TABLE IF EXISTS tmp_ward_processed;
CREATE TABLE tmp_ward_processed (
  pti_id INT NOT NULL,
  province_id INT DEFAULT NULL,
  prefix VARCHAR(50) DEFAULT '',
  title VARCHAR(255) NOT NULL,
  title_full VARCHAR(500) DEFAULT '',
  province_pti_id INT NOT NULL,
  province_title VARCHAR(255) DEFAULT '',
  PRIMARY KEY (pti_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Xử lý dữ liệu và insert vào bảng tạm với mapping province_id
INSERT INTO tmp_ward_processed (
  pti_id, province_id, prefix, title, title_full, 
  province_pti_id, province_title
)
SELECT 
  w.pti_id,
  p.id AS province_id, -- Mapping từ bảng ___province
  TRIM(w.prefix) AS prefix,
  TRIM(w.title) AS title,
  -- Tạo title_full: prefix + title + province_title
  CONCAT(
    TRIM(w.prefix), ' ', 
    TRIM(w.title), ', ', 
    TRIM(w.province_title)
  ) AS title_full,
  w.province_pti_id,
  w.province_title
FROM tmp_ward_import w
LEFT JOIN ___province p ON p.pti_id = w.province_pti_id
WHERE p.id IS NOT NULL; -- Chỉ lấy những ward có province tương ứng

-- Kiểm tra dữ liệu đã xử lý
SELECT COUNT(*) AS total_processed FROM tmp_ward_processed;
SELECT * FROM tmp_ward_processed ORDER BY province_id, pti_id LIMIT 10;

-- Kiểm tra ward không có province mapping
SELECT w.*, 'NO_PROVINCE_MAPPING' AS error
FROM tmp_ward_import w
LEFT JOIN ___province p ON p.pti_id = w.province_pti_id
WHERE p.id IS NULL;

-- =====================================================
-- PHẦN 3: INSERT VÀO BẢNG CHÍNH
-- =====================================================

-- Tạo INSERT statements cho bảng ward
INSERT INTO ward (
  pti_id, province_id, prefix, title, title_full,
  status, created_at, updated_at, created_by, is_merge
)
SELECT 
  w.pti_id,
  w.province_id,
  w.prefix,
  w.title,
  w.title_full,
  2 AS status, -- Mặc định status = 2 (active)
  UNIX_TIMESTAMP() AS created_at,
  UNIX_TIMESTAMP() AS updated_at,
  'import_script' AS created_by,
  2 AS is_merge -- Mặc định is_merge = 2 (new record)
FROM tmp_ward_processed w;

-- =====================================================
-- PHẦN 4: KIỂM TRA SAU KHI IMPORT
-- =====================================================

-- Kiểm tra số lượng records đã import
SELECT COUNT(*) AS total_wards FROM ward WHERE is_merge = 2;

-- Kiểm tra dữ liệu đã import theo province
SELECT 
  p.id AS province_id,
  p.title AS province_title,
  COUNT(w.id) AS ward_count
FROM ___province p
LEFT JOIN ward w ON w.province_id = p.id AND w.is_merge = 2
WHERE p.is_new = 2
GROUP BY p.id, p.title
ORDER BY p.position;

-- Kiểm tra dữ liệu mẫu
SELECT w.id, w.pti_id, w.province_id, w.prefix, w.title, w.title_full
FROM ward w
WHERE w.is_merge = 2
ORDER BY w.province_id, w.id
LIMIT 10;

-- Kiểm tra title_full format
SELECT DISTINCT 
  SUBSTRING(title_full, 1, 50) AS title_full_sample,
  COUNT(*) AS count
FROM ward 
WHERE is_merge = 2
GROUP BY SUBSTRING(title_full, 1, 50)
ORDER BY count DESC
LIMIT 10;

-- =====================================================
-- PHẦN 5: VALIDATION
-- =====================================================

-- Kiểm tra tổng số ward theo province
SELECT 
  'Total wards imported' AS info,
  COUNT(*) AS count
FROM ward 
WHERE is_merge = 2;

-- Kiểm tra province nào chưa có ward
SELECT 
  p.id,
  p.title,
  'NO_WARDS' AS warning
FROM ___province p
LEFT JOIN ward w ON w.province_id = p.id AND w.is_merge = 2
WHERE p.is_new = 2 AND w.id IS NULL;

-- =====================================================
-- PHẦN 6: CLEANUP
-- =====================================================

-- Xóa bảng tạm sau khi hoàn thành
-- DROP TABLE IF EXISTS tmp_ward_import;
-- DROP TABLE IF EXISTS tmp_ward_processed;

-- Lưu ý: Uncomment các lệnh DROP TABLE khi đã kiểm tra xong
