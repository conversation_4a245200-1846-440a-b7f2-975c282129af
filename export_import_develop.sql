-- =====================================================
-- SCRIPT EXPORT/IMPORT DỮ LIỆU CHO MÔI TRƯỜNG DEVELOP
-- =====================================================
-- Dự án: Cập nhật Hệ thống Hành chính Việt Nam
-- Tạo: 2025-07-15
-- Mục đích: Export dữ liệu từ localhost và import vào develop
-- Database: urbox

-- =====================================================
-- PHẦN 1: EXPORT DỮ LIỆU TỪ LOCALHOST
-- =====================================================

-- Chạy trên localhost để export dữ liệu
-- mysqldump -u root -proot -h localhost --single-transaction --routines --triggers urbox tinhthanh xaphuong ward_geometry > urbox_admin_data_export.sql

-- Hoặc export từng bảng riêng:

-- Export bảng tinhthanh
-- mysqldump -u root -proot -h localhost --single-transaction urbox tinhthanh > tinhthanh_export.sql

-- Export bảng xaphuong  
-- mysqldump -u root -proot -h localhost --single-transaction urbox xaphuong > xaphuong_export.sql

-- Export bảng ward_geometry
-- mysqldump -u root -proot -h localhost --single-transaction urbox ward_geometry > ward_geometry_export.sql

-- =====================================================
-- PHẦN 2: TẠO CẤU TRÚC BẢNG TRÊN DEVELOP
-- =====================================================

USE urbox;

-- Tạo bảng tinhthanh
CREATE TABLE IF NOT EXISTS `tinhthanh` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mahc` int(11) NOT NULL COMMENT 'Mã hành chính',
  `tentinh` varchar(255) NOT NULL COMMENT 'Tên tỉnh thành',
  `dientichkm2` varchar(50) DEFAULT NULL COMMENT 'Diện tích km2',
  `dansonguoi` varchar(50) DEFAULT NULL COMMENT 'Dân số người',
  `trungtamhc` varchar(255) DEFAULT NULL COMMENT 'Trung tâm hành chính',
  `kinhdo` decimal(10,6) DEFAULT NULL COMMENT 'Kinh độ',
  `vido` decimal(10,6) DEFAULT NULL COMMENT 'Vĩ độ',
  `truocsapnhap` text DEFAULT NULL COMMENT 'Trước sáp nhập',
  `con` text DEFAULT NULL COMMENT 'Thông tin đơn vị hành chính con',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `mahc` (`mahc`),
  KEY `idx_tentinh` (`tentinh`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tạo bảng xaphuong
CREATE TABLE IF NOT EXISTS `xaphuong` (
  `id` int NOT NULL AUTO_INCREMENT,
  `matinh` int NOT NULL,
  `ma` varchar(10) NOT NULL,
  `tentinh` varchar(255) DEFAULT NULL,
  `loai` varchar(50) DEFAULT NULL,
  `tenhc` varchar(255) NOT NULL,
  `cay` varchar(50) DEFAULT NULL,
  `dientichkm2` int DEFAULT NULL,
  `dansonguoi` varchar(50) DEFAULT NULL,
  `trungtamhc` varchar(255) DEFAULT NULL,
  `kinhdo` decimal(10,6) DEFAULT NULL,
  `vido` decimal(10,6) DEFAULT NULL,
  `truocsapnhap` text DEFAULT NULL,
  `maxa` int DEFAULT NULL,
  `geo_data` longtext DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_matinh` (`matinh`),
  KEY `idx_ma` (`ma`),
  KEY `idx_maxa` (`maxa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tạo bảng ward_geometry
CREATE TABLE IF NOT EXISTS `ward_geometry` (
  `id` int NOT NULL AUTO_INCREMENT,
  `pti_id` int DEFAULT NULL COMMENT 'ID từ maxa (đã trừ 1)',
  `data` json DEFAULT NULL COMMENT 'Geometry data từ API',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `pti_id` (`pti_id`),
  KEY `idx_pti_id` (`pti_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- PHẦN 3: KIỂM TRA DỮ LIỆU SAU KHI IMPORT
-- =====================================================

-- Kiểm tra số lượng bản ghi
SELECT 'KIỂM TRA SỐ LƯỢNG DỮ LIỆU' as info;
SELECT 
    'tinhthanh' as table_name, 
    COUNT(*) as record_count,
    '34' as expected_count
FROM tinhthanh
UNION ALL
SELECT 
    'xaphuong' as table_name, 
    COUNT(*) as record_count,
    '3321' as expected_count
FROM xaphuong
UNION ALL
SELECT 
    'ward_geometry' as table_name, 
    COUNT(*) as record_count,
    '3312' as expected_count
FROM ward_geometry;

-- Kiểm tra dữ liệu mẫu
SELECT 'DỮ LIỆU MẪU TỈNH THÀNH' as info;
SELECT mahc, tentinh FROM tinhthanh ORDER BY mahc LIMIT 5;

SELECT 'DỮ LIỆU MẪU XÃ PHƯỜNG' as info;
SELECT id, matinh, ma, tentinh, loai, tenhc 
FROM xaphuong 
WHERE matinh = 1 
ORDER BY tenhc 
LIMIT 5;

SELECT 'DỮ LIỆU MẪU GEOMETRY' as info;
SELECT id, pti_id, 
       CASE 
           WHEN data IS NOT NULL THEN 'Có geometry data'
           ELSE 'Không có geometry data'
       END as geometry_status
FROM ward_geometry 
ORDER BY id 
LIMIT 5;

-- Kiểm tra tính toàn vẹn dữ liệu
SELECT 'KIỂM TRA TÍNH TOÀN VẸN' as info;
SELECT 
    t.mahc,
    t.tentinh,
    COUNT(x.id) as ward_count
FROM tinhthanh t
LEFT JOIN xaphuong x ON x.matinh = t.mahc
GROUP BY t.mahc, t.tentinh
ORDER BY t.mahc;

-- =====================================================
-- PHẦN 4: HƯỚNG DẪN SỬ DỤNG
-- =====================================================

/*
HƯỚNG DẪN THỰC HIỆN:

1. TRÊN LOCALHOST:
   - Chạy lệnh export để tạo file SQL:
   mysqldump -u root -proot -h localhost --single-transaction --routines --triggers urbox tinhthanh xaphuong ward_geometry > urbox_admin_data_export.sql

2. TRÊN DEVELOP:
   - Upload file urbox_admin_data_export.sql lên server develop
   - Chạy script này để tạo cấu trúc bảng
   - Import dữ liệu: mysql -u username -p urbox < urbox_admin_data_export.sql
   - Chạy phần kiểm tra để verify dữ liệu

3. KIỂM TRA KẾT QUẢ:
   - Tỉnh thành: 34 records
   - Xã phường: 3,321 records  
   - Ward geometry: 3,312 records

4. LƯU Ý:
   - Backup dữ liệu cũ trước khi import
   - Kiểm tra quyền truy cập database
   - Đảm bảo charset utf8mb4 để hỗ trợ tiếng Việt
*/
