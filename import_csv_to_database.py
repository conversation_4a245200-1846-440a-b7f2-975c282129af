#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Import dữ liệu từ file CSV vào database urbox
Dự án: Import 34 tỉnh thành và 3,321 xã phường
Tạo: 2023-07-15
"""

import csv
import re
import unicodedata
import mysql.connector
from datetime import datetime
import time

def remove_vietnamese_accents(text):
    """
    Chuyển đổi tiếng Việt có dấu thành không dấu
    """
    # Normalize unicode
    text = unicodedata.normalize('NFD', text)
    # Remove accents
    text = ''.join(char for char in text if unicodedata.category(char) != 'Mn')
    return text

def create_safe_title(title):
    """
    Tạo safe_title (slug) từ title
    - Bỏ "Tỉnh" hoặc "Thành phố"
    - Chuyển thành chữ thường
    - Bỏ dấu tiếng Việt
    - Thay thế khoảng trắng bằng dấu gạch ngang
    """
    # Bỏ "Tỉnh" hoặc "Thành phố"
    if title.startswith('Thành phố '):
        clean_title = title[10:]  # Bỏ "Thành phố "
    elif title.startswith('Tỉnh '):
        clean_title = title[5:]   # Bỏ "Tỉnh "
    else:
        clean_title = title
    
    # Chuyển thành chữ thường
    clean_title = clean_title.lower()
    
    # Bỏ dấu tiếng Việt
    clean_title = remove_vietnamese_accents(clean_title)
    
    # Thay thế khoảng trắng và ký tự đặc biệt bằng dấu gạch ngang
    clean_title = re.sub(r'[^a-z0-9]+', '-', clean_title)
    
    # Bỏ dấu gạch ngang ở đầu và cuối
    clean_title = clean_title.strip('-')
    
    return clean_title

def is_city(title):
    """
    Kiểm tra xem có phải là thành phố không
    """
    return 1 if title.startswith('Thành phố ') else 0

def create_title_full(prefix, title, province_title):
    """
    Tạo title_full từ prefix, title và province_title
    """
    return f"{prefix.strip()} {title.strip()}, {province_title.strip()}"

def process_province_csv(csv_file_path):
    """
    Xử lý file province.csv và tạo SQL statements
    """
    print("🔄 Đang xử lý file province.csv...")
    
    provinces = []
    
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            province_data = {
                'id': int(row['id']),
                'pti_id': int(row['pti_id']),
                'title': row['title'].strip(),
                'is_city': is_city(row['title']),
                'safe_title': create_safe_title(row['title'])
            }
            provinces.append(province_data)
    
    # Sắp xếp theo safe_title để tạo position
    provinces.sort(key=lambda x: x['safe_title'])
    
    # Tạo SQL statements
    current_time = int(time.time())
    sql_statements = []
    
    for position, province in enumerate(provinces, 1):
        sql = f"""INSERT INTO ___province (
    id, pti_id, title, safe_title, is_city, position, 
    status, created_at, updated_at, created_by, is_new
) VALUES (
    {province['id']}, {province['pti_id']}, '{province['title']}', 
    '{province['safe_title']}', {province['is_city']}, {position},
    2, {current_time}, {current_time}, 'import_script', 2
);"""
        sql_statements.append(sql)
    
    # Ghi ra file SQL
    with open('insert_provinces.sql', 'w', encoding='utf-8') as f:
        f.write("-- Import dữ liệu province từ CSV\n")
        f.write("-- Tạo: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "\n\n")
        f.write("USE urbox;\n\n")
        for sql in sql_statements:
            f.write(sql + "\n")
    
    print(f"✅ Đã xử lý {len(provinces)} provinces")
    print("📄 File SQL đã được tạo: insert_provinces.sql")
    
    return provinces

def process_ward_csv(csv_file_path, provinces):
    """
    Xử lý file ward_new.csv và tạo SQL statements
    """
    print("🔄 Đang xử lý file ward_new.csv...")
    
    # Tạo mapping pti_id -> province_id
    province_mapping = {p['pti_id']: p['id'] for p in provinces}
    
    wards = []
    
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            province_pti_id = int(row['province_pti_id'])
            
            if province_pti_id not in province_mapping:
                print(f"⚠️  Không tìm thấy province cho pti_id: {province_pti_id}")
                continue
            
            ward_data = {
                'pti_id': int(row['pti_id']),
                'province_id': province_mapping[province_pti_id],
                'prefix': row['prefix'].strip(),
                'title': row['title'].strip(),
                'title_full': create_title_full(
                    row['prefix'], 
                    row['title'], 
                    row['province_title']
                )
            }
            wards.append(ward_data)
    
    # Tạo SQL statements
    current_time = int(time.time())
    sql_statements = []
    
    for ward in wards:
        sql = f"""INSERT INTO ward (
    pti_id, province_id, prefix, title, title_full,
    status, created_at, updated_at, created_by, is_merge
) VALUES (
    {ward['pti_id']}, {ward['province_id']}, '{ward['prefix']}', 
    '{ward['title']}', '{ward['title_full']}',
    2, {current_time}, {current_time}, 'import_script', 2
);"""
        sql_statements.append(sql)
    
    # Ghi ra file SQL
    with open('insert_wards.sql', 'w', encoding='utf-8') as f:
        f.write("-- Import dữ liệu ward từ CSV\n")
        f.write("-- Tạo: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "\n\n")
        f.write("USE urbox;\n\n")
        for sql in sql_statements:
            f.write(sql + "\n")
    
    print(f"✅ Đã xử lý {len(wards)} wards")
    print("📄 File SQL đã được tạo: insert_wards.sql")
    
    return wards

def create_validation_sql(provinces, wards):
    """
    Tạo file SQL để validation dữ liệu sau khi import
    """
    validation_sql = f"""-- Validation dữ liệu sau khi import
-- Tạo: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

USE urbox;

-- Kiểm tra số lượng provinces
SELECT 'PROVINCE COUNT' AS info, COUNT(*) AS count 
FROM ___province WHERE is_new = 2;

-- Kiểm tra số lượng wards
SELECT 'WARD COUNT' AS info, COUNT(*) AS count 
FROM ward WHERE is_merge = 2;

-- Kiểm tra is_city distribution
SELECT 
    'CITY DISTRIBUTION' AS info,
    SUM(CASE WHEN is_city = 1 THEN 1 ELSE 0 END) AS cities,
    SUM(CASE WHEN is_city = 0 THEN 1 ELSE 0 END) AS provinces
FROM ___province WHERE is_new = 2;

-- Kiểm tra ward count theo province
SELECT 
    p.id AS province_id,
    p.title AS province_title,
    COUNT(w.id) AS ward_count
FROM ___province p
LEFT JOIN ward w ON w.province_id = p.id AND w.is_merge = 2
WHERE p.is_new = 2
GROUP BY p.id, p.title
ORDER BY p.position;

-- Kiểm tra dữ liệu mẫu
SELECT 'SAMPLE PROVINCES' AS info;
SELECT id, pti_id, title, safe_title, is_city, position 
FROM ___province WHERE is_new = 2 ORDER BY position LIMIT 5;

SELECT 'SAMPLE WARDS' AS info;
SELECT id, pti_id, province_id, prefix, title, title_full 
FROM ward WHERE is_merge = 2 ORDER BY province_id, id LIMIT 5;
"""
    
    with open('validation_import.sql', 'w', encoding='utf-8') as f:
        f.write(validation_sql)
    
    print("📄 File validation SQL đã được tạo: validation_import.sql")

def main():
    """
    Hàm chính để xử lý import dữ liệu
    """
    print("🚀 Bắt đầu xử lý import dữ liệu từ CSV")
    print("=" * 50)
    
    try:
        # Xử lý provinces
        provinces = process_province_csv('province.csv')
        
        # Xử lý wards
        wards = process_ward_csv('ward_new.csv', provinces)
        
        # Tạo validation SQL
        create_validation_sql(provinces, wards)
        
        print("\n" + "=" * 50)
        print("✅ Hoàn thành xử lý dữ liệu!")
        print(f"📊 Tổng kết:")
        print(f"   - Provinces: {len(provinces)}")
        print(f"   - Wards: {len(wards)}")
        print(f"📄 Files đã tạo:")
        print(f"   - insert_provinces.sql")
        print(f"   - insert_wards.sql")
        print(f"   - validation_import.sql")
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    main()
