# Mô Tả Chi Tiết Các File Script

Tài liệu này mô tả chi tiết về tất cả các file đã tạo cho task import dữ liệu từ CSV vào database.

## 📋 Danh Sách File

| File | Loại | Mụ<PERSON> đích | Độ ưu tiên |
|------|------|----------|------------|
| `import_csv_to_database.py` | Python Script | Script chính để xử lý CSV và tạo SQL | ⭐⭐⭐ |
| `import_province_data.sql` | SQL Script | Import dữ liệu province trực tiếp từ CSV | ⭐⭐ |
| `import_ward_data.sql` | SQL Script | Import dữ liệu ward trực tiếp từ CSV | ⭐⭐ |
| `demo_data_processing.py` | Python Demo | Demo xử lý dữ liệu với record mẫu | ⭐ |
| `README.md` | Documentation | Hướng dẫn sử dụng tổng quan | ⭐⭐⭐ |
| `FILE_DESCRIPTIONS.md` | Documentation | Mô tả chi tiết từng file | ⭐ |

---

## 🐍 import_csv_to_database.py

**Mục đích:** Script Python chính để xử lý dữ liệu từ file CSV và tạo ra các file SQL để import vào database.

### Tính năng chính:
- **Xử lý Province Data:**
  - Đọc file `province.csv`
  - Tạo `is_city` (1 cho thành phố, 0 cho tỉnh)
  - Tạo `safe_title` (slug tiếng Việt không dấu)
  - Sắp xếp theo `safe_title` để tạo `position`

- **Xử lý Ward Data:**
  - Đọc file `ward_new.csv`
  - Mapping `province_pti_id` với `province_id`
  - Tạo `title_full` từ `prefix + title + province_title`

- **Tạo Output Files:**
  - `insert_provinces.sql`: SQL statements cho province
  - `insert_wards.sql`: SQL statements cho ward
  - `validation_import.sql`: SQL để kiểm tra dữ liệu

### Cách sử dụng:
```bash
# Đặt file CSV vào cùng thư mục
# province.csv
# ward_new.csv

# Chạy script
python3 import_csv_to_database.py

# Import vào database
mysql -u username -p urbox < insert_provinces.sql
mysql -u username -p urbox < insert_wards.sql

# Kiểm tra kết quả
mysql -u username -p urbox < validation_import.sql
```

### Input Files:
- `province.csv`: id, pti_id, title
- `ward_new.csv`: pti_id, prefix, province_pti_id, province_title, title

### Output Files:
- `insert_provinces.sql`
- `insert_wards.sql`
- `validation_import.sql`

---

## 🗄️ import_province_data.sql

**Mục đích:** Script SQL để import dữ liệu province trực tiếp từ file CSV vào database.

### Quy trình:
1. **Tạo bảng tạm** `tmp_province_import`
2. **Import CSV** bằng `LOAD DATA INFILE`
3. **Xử lý dữ liệu** trong bảng tạm `tmp_province_processed`
4. **Insert vào bảng chính** `___province`
5. **Validation** và cleanup

### Xử lý dữ liệu:
```sql
-- Xử lý is_city
CASE WHEN title LIKE 'Thành phố%' THEN 1 ELSE 0 END

-- Xử lý safe_title
CASE 
  WHEN title LIKE 'Thành phố%' THEN LOWER(REPLACE(SUBSTRING(title, 12), ' ', '-'))
  WHEN title LIKE 'Tỉnh%' THEN LOWER(REPLACE(SUBSTRING(title, 6), ' ', '-'))
  ELSE LOWER(REPLACE(title, ' ', '-'))
END
```

### Lưu ý:
- Cần chỉnh sửa đường dẫn file CSV
- Cần quyền `FILE` trong MySQL
- Charset phải là UTF-8

---

## 🏘️ import_ward_data.sql

**Mục đích:** Script SQL để import dữ liệu ward trực tiếp từ file CSV vào database.

### Quy trình:
1. **Tạo bảng tạm** `tmp_ward_import`
2. **Import CSV** bằng `LOAD DATA INFILE`
3. **Mapping province_id** từ bảng `___province`
4. **Tạo title_full** bằng CONCAT
5. **Insert vào bảng chính** `ward`
6. **Validation** và cleanup

### Xử lý dữ liệu:
```sql
-- Mapping province_id
LEFT JOIN ___province p ON p.pti_id = w.province_pti_id

-- Tạo title_full
CONCAT(TRIM(w.prefix), ' ', TRIM(w.title), ', ', TRIM(w.province_title))
```

### Validation:
- Kiểm tra ward không có province mapping
- Kiểm tra số lượng ward theo province
- Kiểm tra format title_full

---

## 🎯 demo_data_processing.py

**Mục đích:** Script demo để hiểu cách xử lý dữ liệu với một vài record mẫu.

### Tính năng:
- **Demo Province Processing:** Hiển thị cách xử lý is_city và safe_title
- **Demo Ward Processing:** Hiển thị cách tạo title_full và mapping
- **Demo SQL Generation:** Hiển thị SQL statements được tạo
- **Demo Validation:** Hiển thị các query validation

### Dữ liệu mẫu:
```python
# Province samples
{"id": 82, "pti_id": 1, "title": "Thành phố Hà Nội"}
{"id": 83, "pti_id": 4, "title": "Tỉnh Cao Bằng"}

# Ward samples  
{"pti_id": 4, "prefix": "Phường", "province_pti_id": 1, "title": "Ba Đình"}
{"pti_id": 35, "prefix": "Xã", "province_pti_id": 1, "title": "Sóc Sơn"}
```

### Cách chạy:
```bash
python3 demo_data_processing.py
```

---

## 📚 README.md

**Mục đích:** Tài liệu hướng dẫn sử dụng tổng quan cho toàn bộ dự án.

### Nội dung:
- Tổng quan dự án
- Cấu trúc file
- Hướng dẫn cài đặt
- Quy trình import
- Validation
- Troubleshooting

### Đối tượng sử dụng:
- Developer cần import dữ liệu
- QA cần test import process
- DevOps cần deploy script

---

## 🔍 Quy Trình Sử Dụng Khuyến Nghị

### Bước 1: Hiểu rõ quy trình
```bash
# Chạy demo để hiểu cách xử lý
python3 demo_data_processing.py
```

### Bước 2: Chuẩn bị dữ liệu
- Đặt file `province.csv` và `ward_new.csv` vào thư mục
- Kiểm tra format và encoding (UTF-8)

### Bước 3: Tạo SQL files
```bash
# Sử dụng Python script (khuyến nghị)
python3 import_csv_to_database.py
```

### Bước 4: Import vào database
```bash
# Backup trước khi import
mysqldump -u username -p urbox ___province ward > backup.sql

# Import dữ liệu
mysql -u username -p urbox < insert_provinces.sql
mysql -u username -p urbox < insert_wards.sql
```

### Bước 5: Validation
```bash
# Kiểm tra kết quả
mysql -u username -p urbox < validation_import.sql
```

---

## ⚠️ Lưu Ý Quan Trọng

1. **Backup:** Luôn backup database trước khi import
2. **Charset:** Đảm bảo database và file CSV đều dùng UTF-8
3. **Quyền:** MySQL user cần quyền INSERT, CREATE, DROP
4. **File Path:** Chỉnh sửa đường dẫn file CSV trong SQL scripts
5. **Validation:** Luôn chạy validation sau khi import

---

## 🆘 Troubleshooting

### Lỗi thường gặp:
- **File not found:** Kiểm tra đường dẫn file CSV
- **Permission denied:** Kiểm tra quyền MySQL và file system
- **Charset issues:** Đảm bảo UTF-8 encoding
- **Mapping errors:** Kiểm tra dữ liệu province đã import chưa
